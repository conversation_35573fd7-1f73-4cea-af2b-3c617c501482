<?php

namespace App\Http\Controllers\AdminDaerah;

use App\Http\Controllers\Controller;
use App\Models\Pendaftaran;
use App\Models\Peserta;
use App\Models\Golongan;
use App\Models\DokumenPeserta;
use App\Models\Pembayaran;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;
use Inertia\Response;

class PendaftaranController extends Controller
{
    /**
     * Display a listing of pendaftaran in admin daerah's wilayah
     */
    public function index(): Response
    {
        $adminWilayah = Auth::user()->id_wilayah;

        $pendaftaran = Pendaftaran::with([
            'peserta.user',
            'peserta.wilayah',
            'golongan.cabangLomba',
            'pembayaran',
            'dokumenPeserta'
        ])
        ->whereHas('peserta', function($q) use ($adminWilayah) {
            $q->where('id_wilayah', $adminWilayah);
        })
        ->orderBy('created_at', 'desc')
        ->paginate(20);

        // Get available golongan for filtering
        $golongan = Golongan::with('cabangLomba')
            ->where('status', 'aktif')
            ->orderBy('nama_golongan')
            ->get();

        return Inertia::render('AdminDaerah/Pendaftaran/Index', [
            'pendaftaran' => $pendaftaran,
            'golongan' => $golongan
        ]);
    }

    /**
     * Show the form for creating a new pendaftaran
     */
    public function create(Request $request): Response
    {
        $adminWilayah = Auth::user()->id_wilayah;

        // Get peserta from admin's wilayah
        $peserta = Peserta::with('user')
            ->where('id_wilayah', $adminWilayah)
            ->where('status_peserta', 'approved')
            ->orderBy('nama_lengkap')
            ->get();

        // Get available golongan
        $golongan = Golongan::with('cabangLomba')
            ->where('status', 'aktif')
            ->orderBy('nama_golongan')
            ->get();

        // If peserta ID is provided, get the specific peserta
        $selectedPeserta = null;
        if ($request->has('peserta')) {
            $selectedPeserta = Peserta::with('user')
                ->where('id_wilayah', $adminWilayah)
                ->findOrFail($request->peserta);
        }

        return Inertia::render('AdminDaerah/Pendaftaran/Create', [
            'peserta' => $peserta,
            'golongan' => $golongan,
            'selectedPeserta' => $selectedPeserta
        ]);
    }

    /**
     * Store a newly created pendaftaran
     */
    public function store(Request $request)
    {
        $adminWilayah = Auth::user()->id_wilayah;

        $validated = $request->validate([
            'id_peserta' => 'required|exists:peserta,id_peserta',
            'id_golongan' => 'required|exists:golongan,id_golongan',
            'nomor_urut' => 'nullable|integer|min:1',
        ]);

        // Verify peserta belongs to admin's wilayah
        $peserta = Peserta::where('id_peserta', $validated['id_peserta'])
            ->where('id_wilayah', $adminWilayah)
            ->firstOrFail();

        // Check if peserta already registered for this golongan
        $existingPendaftaran = Pendaftaran::where('id_peserta', $validated['id_peserta'])
            ->where('id_golongan', $validated['id_golongan'])
            ->first();

        if ($existingPendaftaran) {
            return back()->withErrors(['id_golongan' => 'Peserta sudah terdaftar pada golongan ini.']);
        }

        $golongan = Golongan::findOrFail($validated['id_golongan']);

        DB::transaction(function () use ($validated, $golongan, $peserta) {
            // Generate nomor urut if not provided
            if (!isset($validated['nomor_urut'])) {
                $lastNomor = Pendaftaran::where('id_golongan', $validated['id_golongan'])
                    ->max('nomor_urut') ?? $golongan->nomor_urut_awal - 1;
                $validated['nomor_urut'] = $lastNomor + 1;
            }

            // Create pendaftaran
            $pendaftaran = Pendaftaran::create([
                'id_peserta' => $validated['id_peserta'],
                'id_golongan' => $validated['id_golongan'],
                'nomor_urut' => $validated['nomor_urut'],
                'biaya_pendaftaran' => $golongan->biaya_pendaftaran,
                'status_pendaftaran' => 'submitted', // Admin daerah can directly submit
                'tanggal_daftar' => now(),
                'registered_by' => Auth::id(),
            ]);

            // Create pembayaran record
            Pembayaran::create([
                'id_pendaftaran' => $pendaftaran->id_pendaftaran,
                'jumlah_bayar' => $golongan->biaya_pendaftaran,
                'metode_pembayaran' => 'transfer',
                'status_pembayaran' => 'pending',
                'tanggal_bayar' => now(),
            ]);
        });

        return redirect()->route('admin-daerah.pendaftaran.index')
            ->with('success', 'Pendaftaran berhasil dibuat.');
    }

    /**
     * Display the specified pendaftaran
     */
    public function show(string $id): Response
    {
        $adminWilayah = Auth::user()->id_wilayah;

        $pendaftaran = Pendaftaran::with([
            'peserta.user',
            'peserta.wilayah',
            'golongan.cabangLomba',
            'pembayaran',
            'dokumenPeserta'
        ])
        ->whereHas('peserta', function($q) use ($adminWilayah) {
            $q->where('id_wilayah', $adminWilayah);
        })
        ->findOrFail($id);

        return Inertia::render('AdminDaerah/Pendaftaran/Show', [
            'pendaftaran' => $pendaftaran
        ]);
    }

    /**
     * Show the form for editing the specified pendaftaran
     */
    public function edit(string $id): Response
    {
        $adminWilayah = Auth::user()->id_wilayah;

        $pendaftaran = Pendaftaran::with([
            'peserta.user',
            'golongan.cabangLomba'
        ])
        ->whereHas('peserta', function($q) use ($adminWilayah) {
            $q->where('id_wilayah', $adminWilayah);
        })
        ->findOrFail($id);

        // Get available golongan
        $golongan = Golongan::with('cabangLomba')
            ->where('status', 'aktif')
            ->orderBy('nama_golongan')
            ->get();

        return Inertia::render('AdminDaerah/Pendaftaran/Edit', [
            'pendaftaran' => $pendaftaran,
            'golongan' => $golongan
        ]);
    }

    /**
     * Update the specified pendaftaran
     */
    public function update(Request $request, string $id)
    {
        $adminWilayah = Auth::user()->id_wilayah;

        $pendaftaran = Pendaftaran::whereHas('peserta', function($q) use ($adminWilayah) {
            $q->where('id_wilayah', $adminWilayah);
        })->findOrFail($id);

        $validated = $request->validate([
            'id_golongan' => 'required|exists:golongan,id_golongan',
            'nomor_urut' => 'nullable|integer|min:1',
        ]);

        // Check if golongan changed and no conflict
        if ($pendaftaran->id_golongan != $validated['id_golongan']) {
            $existingPendaftaran = Pendaftaran::where('id_peserta', $pendaftaran->id_peserta)
                ->where('id_golongan', $validated['id_golongan'])
                ->where('id_pendaftaran', '!=', $id)
                ->first();

            if ($existingPendaftaran) {
                return back()->withErrors(['id_golongan' => 'Peserta sudah terdaftar pada golongan ini.']);
            }
        }

        $golongan = Golongan::findOrFail($validated['id_golongan']);

        $pendaftaran->update([
            'id_golongan' => $validated['id_golongan'],
            'nomor_urut' => $validated['nomor_urut'] ?? $pendaftaran->nomor_urut,
            'biaya_pendaftaran' => $golongan->biaya_pendaftaran,
        ]);

        // Update pembayaran amount if golongan changed
        if ($pendaftaran->pembayaran && $pendaftaran->wasChanged('biaya_pendaftaran')) {
            $pendaftaran->pembayaran->update([
                'jumlah_bayar' => $golongan->biaya_pendaftaran
            ]);
        }

        return redirect()->route('admin-daerah.pendaftaran.index')
            ->with('success', 'Pendaftaran berhasil diperbarui.');
    }

    /**
     * Remove the specified pendaftaran
     */
    public function destroy(string $id)
    {
        $adminWilayah = Auth::user()->id_wilayah;

        $pendaftaran = Pendaftaran::whereHas('peserta', function($q) use ($adminWilayah) {
            $q->where('id_wilayah', $adminWilayah);
        })->findOrFail($id);

        // Only allow deletion if not yet approved
        if ($pendaftaran->status_pendaftaran === 'approved') {
            return back()->withErrors(['error' => 'Pendaftaran yang sudah disetujui tidak dapat dihapus.']);
        }

        $pendaftaran->delete();

        return redirect()->route('admin-daerah.pendaftaran.index')
            ->with('success', 'Pendaftaran berhasil dihapus.');
    }

    /**
     * Submit pendaftaran for approval
     */
    public function submit(string $id)
    {
        $adminWilayah = Auth::user()->id_wilayah;

        $pendaftaran = Pendaftaran::whereHas('peserta', function($q) use ($adminWilayah) {
            $q->where('id_wilayah', $adminWilayah);
        })->findOrFail($id);

        if ($pendaftaran->status_pendaftaran !== 'draft') {
            return back()->withErrors(['error' => 'Pendaftaran sudah disubmit atau disetujui.']);
        }

        $pendaftaran->update([
            'status_pendaftaran' => 'submitted',
            'tanggal_daftar' => now(),
        ]);

        return back()->with('success', 'Pendaftaran berhasil disubmit untuk persetujuan.');
    }

    /**
     * Get documents for pendaftaran
     */
    public function documents(string $id): Response
    {
        $adminWilayah = Auth::user()->id_wilayah;

        $pendaftaran = Pendaftaran::with([
            'peserta.user',
            'dokumenPeserta'
        ])
        ->whereHas('peserta', function($q) use ($adminWilayah) {
            $q->where('id_wilayah', $adminWilayah);
        })
        ->findOrFail($id);

        return Inertia::render('AdminDaerah/Pendaftaran/Documents', [
            'pendaftaran' => $pendaftaran
        ]);
    }
}
