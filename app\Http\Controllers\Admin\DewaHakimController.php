<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\DewaHakim;
use App\Models\User;
use App\Models\Wilayah;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Inertia\Inertia;

class DewaHakimController extends Controller
{
    /**
     * Display a listing of dewan hakim.
     */
    public function index(Request $request)
    {
        $query = DewaHakim::with(['user', 'wilayah'])
            ->when($request->search, function ($query, $search) {
                $query->where(function ($q) use ($search) {
                    $q->where('nama_lengkap', 'like', "%{$search}%")
                      ->orWhere('nik', 'like', "%{$search}%")
                      ->orWhere('pekerjaan', 'like', "%{$search}%")
                      ->orWhereHas('user', function ($q) use ($search) {
                          $q->where('username', 'like', "%{$search}%")
                            ->orWhere('email', 'like', "%{$search}%");
                      });
                });
            })
            ->when($request->tipe_hakim, function ($query, $tipe) {
                $query->where('tipe_hakim', $tipe);
            })
            ->when($request->status, function ($query, $status) {
                $query->where('status', $status);
            })
            ->when($request->wilayah, function ($query, $wilayah) {
                $query->where('id_wilayah', $wilayah);
            });

        $dewaHakim = $query->latest()->paginate(15)->withQueryString();

        return Inertia::render('Admin/DewaHakim/Index', [
            'dewaHakim' => $dewaHakim,
            'filters' => $request->only(['search', 'tipe_hakim', 'status', 'wilayah']),
            'wilayah' => Wilayah::aktif()->get(),
            'tipeHakim' => [
                'undangan' => 'Undangan',
                'kabupaten' => 'Kabupaten'
            ]
        ]);
    }

    /**
     * Show the form for creating a new dewan hakim.
     */
    public function create()
    {
        // Get users with role dewan_hakim that don't have dewan hakim profile yet
        $availableUsers = User::where('role', 'dewan_hakim')
            ->whereDoesntHave('dewaHakim')
            ->get();

        return Inertia::render('Admin/DewaHakim/Create', [
            'availableUsers' => $availableUsers,
            'wilayah' => Wilayah::aktif()->get(),
            'tipeHakim' => [
                'undangan' => 'Undangan',
                'kabupaten' => 'Kabupaten'
            ]
        ]);
    }

    /**
     * Store a newly created dewan hakim in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'id_user' => 'required|exists:users,id_user|unique:dewan_hakim',
            'nik' => 'required|string|size:16|unique:dewan_hakim',
            'nama_lengkap' => 'required|string|max:100',
            'tempat_lahir' => 'required|string|max:50',
            'tanggal_lahir' => 'required|date|before:today',
            'pekerjaan' => 'required|string|max:100',
            'unit_kerja' => 'required|string|max:100',
            'alamat_rumah' => 'required|string',
            'alamat_kantor' => 'nullable|string',
            'no_telepon' => 'required|string|max:15',
            'spesialisasi' => 'required|string|max:100',
            'tipe_hakim' => ['required', Rule::in(['undangan', 'kabupaten'])],
            'id_wilayah' => 'required|exists:wilayah,id_wilayah',
            'status' => ['required', Rule::in(['aktif', 'non_aktif'])]
        ]);

        DewaHakim::create($validated);

        return redirect()->route('admin.dewan-hakim.index')
            ->with('success', 'Dewan hakim berhasil dibuat.');
    }

    /**
     * Display the specified dewan hakim.
     */
    public function show(DewaHakim $dewaHakim)
    {
        $dewaHakim->load(['user', 'wilayah', 'pendidikan', 'pengalaman', 'prestasi', 'nilaiPeserta.pendaftaran.peserta']);

        return Inertia::render('Admin/DewaHakim/Show', [
            'dewaHakim' => $dewaHakim
        ]);
    }

    /**
     * Show the form for editing the specified dewan hakim.
     */
    public function edit(DewaHakim $dewaHakim)
    {
        return Inertia::render('Admin/DewaHakim/Edit', [
            'dewaHakim' => $dewaHakim,
            'wilayah' => Wilayah::aktif()->get(),
            'tipeHakim' => [
                'undangan' => 'Undangan',
                'kabupaten' => 'Kabupaten'
            ]
        ]);
    }

    /**
     * Update the specified dewan hakim in storage.
     */
    public function update(Request $request, DewaHakim $dewaHakim)
    {
        $validated = $request->validate([
            'nik' => ['required', 'string', 'size:16', Rule::unique('dewan_hakim')->ignore($dewaHakim->id_dewan_hakim, 'id_dewan_hakim')],
            'nama_lengkap' => 'required|string|max:100',
            'tempat_lahir' => 'required|string|max:50',
            'tanggal_lahir' => 'required|date|before:today',
            'pekerjaan' => 'required|string|max:100',
            'unit_kerja' => 'required|string|max:100',
            'alamat_rumah' => 'required|string',
            'alamat_kantor' => 'nullable|string',
            'no_telepon' => 'required|string|max:15',
            'spesialisasi' => 'required|string|max:100',
            'tipe_hakim' => ['required', Rule::in(['undangan', 'kabupaten'])],
            'id_wilayah' => 'required|exists:wilayah,id_wilayah',
            'status' => ['required', Rule::in(['aktif', 'non_aktif'])]
        ]);

        $dewaHakim->update($validated);

        return redirect()->route('admin.dewan-hakim.index')
            ->with('success', 'Dewan hakim berhasil diperbarui.');
    }

    /**
     * Remove the specified dewan hakim from storage.
     */
    public function destroy(DewaHakim $dewaHakim)
    {
        // Check if dewan hakim has nilai peserta
        if ($dewaHakim->nilaiPeserta()->count() > 0) {
            return back()->with('error', 'Dewan hakim tidak dapat dihapus karena memiliki data penilaian.');
        }

        $dewaHakim->delete();

        return redirect()->route('admin.dewan-hakim.index')
            ->with('success', 'Dewan hakim berhasil dihapus.');
    }

    /**
     * Toggle dewan hakim status.
     */
    public function toggleStatus(DewaHakim $dewaHakim)
    {
        $newStatus = $dewaHakim->status === 'aktif' ? 'non_aktif' : 'aktif';
        $dewaHakim->update(['status' => $newStatus]);

        return back()->with('success', "Status dewan hakim berhasil diubah menjadi {$newStatus}.");
    }

    /**
     * Show detailed profile of dewan hakim.
     */
    public function profile(DewaHakim $dewaHakim)
    {
        $dewaHakim->load(['user', 'wilayah', 'pendidikan', 'pengalaman', 'prestasi']);

        return Inertia::render('Admin/DewaHakim/Profile', [
            'dewaHakim' => $dewaHakim
        ]);
    }
}
