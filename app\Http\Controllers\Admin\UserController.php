<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Wilayah;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;
use Inertia\Inertia;

class UserController extends Controller
{
    /**
     * Display a listing of users.
     */
    public function index(Request $request)
    {
        $query = User::with(['wilayah', 'createdBy'])
            ->when($request->search, function ($query, $search) {
                $query->where(function ($q) use ($search) {
                    $q->where('nama_lengkap', 'like', "%{$search}%")
                      ->orWhere('username', 'like', "%{$search}%")
                      ->orWhere('email', 'like', "%{$search}%");
                });
            })
            ->when($request->role, function ($query, $role) {
                $query->where('role', $role);
            })
            ->when($request->status, function ($query, $status) {
                $query->where('status', $status);
            })
            ->when($request->wilayah, function ($query, $wilayah) {
                $query->where('id_wilayah', $wilayah);
            });

        $users = $query->latest()->paginate(15)->withQueryString();

        return Inertia::render('Admin/Users/<USER>', [
            'users' => $users,
            'filters' => $request->only(['search', 'role', 'status', 'wilayah']),
            'wilayah' => Wilayah::aktif()->get(),
            'roles' => [
                'superadmin' => 'Super Admin',
                'admin' => 'Admin',
                'admin_daerah' => 'Admin Daerah',
                'peserta' => 'Peserta',
                'dewan_hakim' => 'Dewan Hakim'
            ]
        ]);
    }

    /**
     * Show the form for creating a new user.
     */
    public function create()
    {
        return Inertia::render('Admin/Users/<USER>', [
            'wilayah' => Wilayah::aktif()->get(),
            'roles' => [
                'admin' => 'Admin',
                'admin_daerah' => 'Admin Daerah',
                'dewan_hakim' => 'Dewan Hakim'
            ]
        ]);
    }

    /**
     * Store a newly created user in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'username' => 'required|string|max:50|unique:users',
            'email' => 'required|string|email|max:100|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'role' => ['required', Rule::in(['admin', 'admin_daerah', 'dewan_hakim'])],
            'nama_lengkap' => 'required|string|max:100',
            'no_telepon' => 'nullable|string|max:20',
            'id_wilayah' => 'nullable|exists:wilayah,id_wilayah',
            'status' => ['required', Rule::in(['aktif', 'non_aktif'])]
        ]);

        $validated['password'] = Hash::make($validated['password']);
        $validated['created_by'] = auth()->id();

        User::create($validated);

        return redirect()->route('admin.users.index')
            ->with('success', 'User berhasil dibuat.');
    }

    /**
     * Display the specified user.
     */
    public function show(User $user)
    {
        $user->load(['wilayah', 'createdBy', 'peserta', 'dewaHakim']);

        return Inertia::render('Admin/Users/<USER>', [
            'user' => $user
        ]);
    }

    /**
     * Show the form for editing the specified user.
     */
    public function edit(User $user)
    {
        return Inertia::render('Admin/Users/<USER>', [
            'user' => $user,
            'wilayah' => Wilayah::aktif()->get(),
            'roles' => [
                'admin' => 'Admin',
                'admin_daerah' => 'Admin Daerah',
                'dewan_hakim' => 'Dewan Hakim'
            ]
        ]);
    }

    /**
     * Update the specified user in storage.
     */
    public function update(Request $request, User $user)
    {
        $validated = $request->validate([
            'username' => ['required', 'string', 'max:50', Rule::unique('users')->ignore($user->id_user, 'id_user')],
            'email' => ['required', 'string', 'email', 'max:100', Rule::unique('users')->ignore($user->id_user, 'id_user')],
            'role' => ['required', Rule::in(['admin', 'admin_daerah', 'dewan_hakim'])],
            'nama_lengkap' => 'required|string|max:100',
            'no_telepon' => 'nullable|string|max:20',
            'id_wilayah' => 'nullable|exists:wilayah,id_wilayah',
            'status' => ['required', Rule::in(['aktif', 'non_aktif', 'suspended'])]
        ]);

        $user->update($validated);

        return redirect()->route('admin.users.index')
            ->with('success', 'User berhasil diperbarui.');
    }

    /**
     * Remove the specified user from storage.
     */
    public function destroy(User $user)
    {
        // Prevent deletion of superadmin and current user
        if ($user->role === 'superadmin' || $user->id_user === auth()->id()) {
            return back()->with('error', 'User tidak dapat dihapus.');
        }

        $user->delete();

        return redirect()->route('admin.users.index')
            ->with('success', 'User berhasil dihapus.');
    }

    /**
     * Toggle user status.
     */
    public function toggleStatus(User $user)
    {
        $newStatus = $user->status === 'aktif' ? 'non_aktif' : 'aktif';
        $user->update(['status' => $newStatus]);

        return back()->with('success', "Status user berhasil diubah menjadi {$newStatus}.");
    }

    /**
     * Reset user password.
     */
    public function resetPassword(Request $request, User $user)
    {
        $validated = $request->validate([
            'password' => 'required|string|min:8|confirmed'
        ]);

        $user->update([
            'password' => Hash::make($validated['password'])
        ]);

        return back()->with('success', 'Password user berhasil direset.');
    }
}
