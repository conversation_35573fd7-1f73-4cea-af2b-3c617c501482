<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\NilaiPeserta;
use App\Models\Pendaftaran;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ScoringController extends Controller
{
    /**
     * Display a listing of scores
     */
    public function index(Request $request): JsonResponse
    {
        $user = Auth::user();
        $dewaHakim = $user->dewaHakim;

        if (!$dewaHakim) {
            return response()->json([
                'success' => false,
                'message' => 'Judge profile not found'
            ], 404);
        }

        $query = NilaiPeserta::with([
            'pendaftaran.peserta.user',
            'pendaftaran.peserta.wilayah',
            'pendaftaran.golongan.cabangLomba',
            'dewaHakim'
        ])->where('id_dewan_hakim', $dewaHakim->id_dewan_hakim);

        // Search functionality
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->whereHas('pendaftaran', function ($q) use ($search) {
                $q->where('nomor_peserta', 'like', "%{$search}%")
                  ->orWhereHas('peserta', function ($q) use ($search) {
                      $q->where('nama_lengkap', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by competition category
        if ($request->has('id_golongan') && $request->id_golongan) {
            $query->whereHas('pendaftaran', function ($q) use ($request) {
                $q->where('id_golongan', $request->id_golongan);
            });
        }

        $scores = $query->orderBy('created_at', 'desc')->paginate(20);

        return response()->json([
            'success' => true,
            'data' => $scores
        ]);
    }

    /**
     * Store a newly created score
     */
    public function store(Request $request): JsonResponse
    {
        $user = Auth::user();
        $dewaHakim = $user->dewaHakim;

        if (!$dewaHakim) {
            return response()->json([
                'success' => false,
                'message' => 'Judge profile not found'
            ], 404);
        }

        $validated = $request->validate([
            'id_pendaftaran' => 'required|exists:pendaftaran,id_pendaftaran',
            'nilai_tajwid' => 'required|numeric|min:0|max:100',
            'nilai_fashohah' => 'required|numeric|min:0|max:100',
            'nilai_suara' => 'required|numeric|min:0|max:100',
            'nilai_adab' => 'required|numeric|min:0|max:100',
            'catatan' => 'nullable|string|max:1000',
        ]);

        $pendaftaran = Pendaftaran::findOrFail($validated['id_pendaftaran']);

        // Check if judge can score this participant
        if (!$this->canJudgeScore($dewaHakim, $pendaftaran)) {
            return response()->json([
                'success' => false,
                'message' => 'You are not authorized to score this participant'
            ], 403);
        }

        // Check if already scored
        $existingScore = NilaiPeserta::where('id_pendaftaran', $validated['id_pendaftaran'])
            ->where('id_dewan_hakim', $dewaHakim->id_dewan_hakim)
            ->first();

        if ($existingScore) {
            return response()->json([
                'success' => false,
                'message' => 'You have already scored this participant'
            ], 422);
        }

        // Calculate total score
        $totalNilai = ($validated['nilai_tajwid'] + $validated['nilai_fashohah'] + 
                      $validated['nilai_suara'] + $validated['nilai_adab']) / 4;

        $score = NilaiPeserta::create([
            'id_pendaftaran' => $validated['id_pendaftaran'],
            'id_dewan_hakim' => $dewaHakim->id_dewan_hakim,
            'nilai_tajwid' => $validated['nilai_tajwid'],
            'nilai_fashohah' => $validated['nilai_fashohah'],
            'nilai_suara' => $validated['nilai_suara'],
            'nilai_adab' => $validated['nilai_adab'],
            'total_nilai' => $totalNilai,
            'catatan' => $validated['catatan'],
            'tanggal_penilaian' => now(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Score submitted successfully',
            'data' => $score->load([
                'pendaftaran.peserta.user',
                'pendaftaran.golongan.cabangLomba',
                'dewaHakim'
            ])
        ], 201);
    }

    /**
     * Display the specified score
     */
    public function show(string $id): JsonResponse
    {
        $user = Auth::user();
        $dewaHakim = $user->dewaHakim;

        $score = NilaiPeserta::with([
            'pendaftaran.peserta.user',
            'pendaftaran.peserta.wilayah',
            'pendaftaran.golongan.cabangLomba',
            'dewaHakim'
        ])->findOrFail($id);

        // Check if judge can view this score
        if ($score->id_dewan_hakim !== $dewaHakim->id_dewan_hakim) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        return response()->json([
            'success' => true,
            'data' => $score
        ]);
    }

    /**
     * Update the specified score
     */
    public function update(Request $request, string $id): JsonResponse
    {
        $user = Auth::user();
        $dewaHakim = $user->dewaHakim;

        $score = NilaiPeserta::findOrFail($id);

        // Check if judge can update this score
        if ($score->id_dewan_hakim !== $dewaHakim->id_dewan_hakim) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        $validated = $request->validate([
            'nilai_tajwid' => 'required|numeric|min:0|max:100',
            'nilai_fashohah' => 'required|numeric|min:0|max:100',
            'nilai_suara' => 'required|numeric|min:0|max:100',
            'nilai_adab' => 'required|numeric|min:0|max:100',
            'catatan' => 'nullable|string|max:1000',
        ]);

        // Calculate total score
        $totalNilai = ($validated['nilai_tajwid'] + $validated['nilai_fashohah'] + 
                      $validated['nilai_suara'] + $validated['nilai_adab']) / 4;

        $score->update([
            'nilai_tajwid' => $validated['nilai_tajwid'],
            'nilai_fashohah' => $validated['nilai_fashohah'],
            'nilai_suara' => $validated['nilai_suara'],
            'nilai_adab' => $validated['nilai_adab'],
            'total_nilai' => $totalNilai,
            'catatan' => $validated['catatan'],
            'updated_at' => now(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Score updated successfully',
            'data' => $score->load([
                'pendaftaran.peserta.user',
                'pendaftaran.golongan.cabangLomba',
                'dewaHakim'
            ])
        ]);
    }

    /**
     * Get participant details for scoring
     */
    public function participant(string $registrationId): JsonResponse
    {
        $user = Auth::user();
        $dewaHakim = $user->dewaHakim;

        if (!$dewaHakim) {
            return response()->json([
                'success' => false,
                'message' => 'Judge profile not found'
            ], 404);
        }

        $pendaftaran = Pendaftaran::with([
            'peserta.user',
            'peserta.wilayah',
            'golongan.cabangLomba',
            'mimbar',
            'nilaiPeserta' => function ($q) use ($dewaHakim) {
                $q->where('id_dewan_hakim', $dewaHakim->id_dewan_hakim);
            }
        ])->findOrFail($registrationId);

        // Check if judge can score this participant
        if (!$this->canJudgeScore($dewaHakim, $pendaftaran)) {
            return response()->json([
                'success' => false,
                'message' => 'You are not authorized to score this participant'
            ], 403);
        }

        return response()->json([
            'success' => true,
            'data' => $pendaftaran
        ]);
    }

    /**
     * Check if judge can score a participant
     */
    private function canJudgeScore($dewaHakim, $pendaftaran): bool
    {
        // Check if participant is approved/verified
        if (!in_array($pendaftaran->status_pendaftaran, ['approved', 'verified'])) {
            return false;
        }

        // Check judge specialization
        if ($dewaHakim->spesialisasi) {
            $cabangNama = $pendaftaran->golongan->cabangLomba->nama_cabang;
            if (stripos($cabangNama, $dewaHakim->spesialisasi) === false) {
                return false;
            }
        }

        // Check regional assignment
        if ($dewaHakim->tipe_hakim === 'kabupaten' && $dewaHakim->id_wilayah) {
            if ($pendaftaran->peserta->id_wilayah !== $dewaHakim->id_wilayah) {
                return false;
            }
        }

        return true;
    }
}
