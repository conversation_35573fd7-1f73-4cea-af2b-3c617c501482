<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Peserta;

use App\Models\Wilayah;
use App\Models\Golongan;
use App\Models\CabangLomba;
use App\Models\Pendaftaran;
use App\Models\JenisNilai;
use App\Rules\UniqueNikAcrossTables;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Hash;
use Tests\TestCase;

class DatabaseApiConsistencyTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $wilayah;
    protected $cabangLomba;
    protected $golongan;
    protected $jenisNilai;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data
        $this->createTestData();
    }

    private function createTestData()
    {
        // Create wilayah
        $this->wilayah = Wilayah::create([
            'kode_wilayah' => 'LP01',
            'nama_wilayah' => 'Lampung Selatan',
            'level_wilayah' => 'kabupaten',
            'status' => 'aktif'
        ]);

        // Create cabang lomba
        $this->cabangLomba = CabangLomba::create([
            'kode_cabang' => 'TIL',
            'nama_cabang' => 'Tilawah',
            'deskripsi' => 'Test tilawah',
            'status' => 'aktif'
        ]);

        // Create golongan
        $this->golongan = Golongan::create([
            'kode_golongan' => 'TIL-AP',
            'nama_golongan' => 'Tilawah Anak Putra',
            'id_cabang' => $this->cabangLomba->id_cabang,
            'jenis_kelamin' => 'L',
            'batas_umur_min' => 6,
            'batas_umur_max' => 12,
            'kuota_max' => 50,
            'biaya_pendaftaran' => 100000,
            'nomor_urut_awal' => 1,
            'nomor_urut_akhir' => 50,
            'status' => 'aktif'
        ]);

        // Create jenis nilai
        $this->jenisNilai = [
            JenisNilai::create([
                'nama_jenis' => 'Tajwid',
                'kode_jenis' => 'TAJ',
                'keterangan' => 'Kaidah tajwid',
                'bobot_nilai' => 1.0,
                'nilai_minimum' => 0,
                'nilai_maksimum' => 100,
                'status' => 'aktif'
            ]),
            JenisNilai::create([
                'nama_jenis' => 'Fashohah',
                'kode_jenis' => 'FAS',
                'keterangan' => 'Kebenaran bacaan',
                'bobot_nilai' => 1.0,
                'nilai_minimum' => 0,
                'nilai_maksimum' => 100,
                'status' => 'aktif'
            ])
        ];
    }

    /**
     * Test gender field consistency between API and database
     */
    public function test_gender_field_consistency()
    {


        // Test direct model creation with L/P values (database format)
        $user = User::create([
            'username' => 'testuser',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'nama_lengkap' => 'Test User',
            'role' => 'peserta',
            'status' => 'aktif',
            'id_wilayah' => $this->wilayah->id_wilayah
        ]);

        $peserta = Peserta::create([
            'id_user' => $user->id_user,
            'nik' => '1234567890123456',
            'nama_lengkap' => 'Test Participant',
            'tempat_lahir' => 'Jakarta',
            'tanggal_lahir' => '2010-01-01',
            'jenis_kelamin' => 'L', // Database format
            'alamat' => 'Test Address',
            'id_wilayah' => $this->wilayah->id_wilayah
        ]);

        // Verify data is stored correctly in database
        $this->assertDatabaseHas('peserta', [
            'nik' => '1234567890123456',
            'jenis_kelamin' => 'L'
        ]);

        // Test that the model accepts L/P values
        $this->assertEquals('L', $peserta->jenis_kelamin);
    }

    /**
     * Test NIK uniqueness across peserta and dewan_hakim tables
     */
    public function test_nik_uniqueness_across_tables()
    {
        $nik = '1234567890123456';

        // Create a peserta with NIK
        $user1 = User::create([
            'username' => 'peserta1',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'nama_lengkap' => 'Peserta Test',
            'role' => 'peserta',
            'status' => 'aktif',
            'id_wilayah' => $this->wilayah->id_wilayah
        ]);

        Peserta::create([
            'id_user' => $user1->id_user,
            'nik' => $nik,
            'nama_lengkap' => 'Peserta Test',
            'tempat_lahir' => 'Jakarta',
            'tanggal_lahir' => '1990-01-01',
            'jenis_kelamin' => 'L',
            'alamat' => 'Test Address',
            'id_wilayah' => $this->wilayah->id_wilayah
        ]);

        // Test the validation rule
        $rule = new UniqueNikAcrossTables();
        $fails = false;

        $rule->validate('nik', $nik, function() use (&$fails) {
            $fails = true;
        });

        $this->assertTrue($fails, 'NIK uniqueness validation should fail for duplicate NIK');
    }

    /**
     * Test nomor_urut field exists in pendaftaran table
     */
    public function test_nomor_urut_field_exists()
    {
        // Create test user and peserta
        $user = User::create([
            'username' => 'testuser2',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'nama_lengkap' => 'Test User 2',
            'role' => 'peserta',
            'status' => 'aktif',
            'id_wilayah' => $this->wilayah->id_wilayah
        ]);

        $peserta = Peserta::create([
            'id_user' => $user->id_user,
            'nik' => '1234567890123457',
            'nama_lengkap' => 'Test Participant 2',
            'tempat_lahir' => 'Jakarta',
            'tanggal_lahir' => '2010-01-01',
            'jenis_kelamin' => 'L',
            'alamat' => 'Test Address',
            'id_wilayah' => $this->wilayah->id_wilayah
        ]);

        // Create pendaftaran with nomor_urut
        $pendaftaran = Pendaftaran::create([
            'id_peserta' => $peserta->id_peserta,
            'id_golongan' => $this->golongan->id_golongan,
            'nomor_pendaftaran' => 'REG2025000001',
            'nomor_peserta' => 'TIL-AP001',
            'nomor_urut' => 1, // This should work now
            'tahun_pendaftaran' => 2025,
            'status_pendaftaran' => 'draft',
            'tanggal_daftar' => now()
        ]);

        $this->assertDatabaseHas('pendaftaran', [
            'id_pendaftaran' => $pendaftaran->id_pendaftaran,
            'nomor_urut' => 1
        ]);
    }

    /**
     * Test flexible scoring system
     */
    public function test_flexible_scoring_system()
    {
        // Verify jenis_nilai records exist
        $this->assertCount(2, $this->jenisNilai);

        // Test that each jenis_nilai has proper validation methods
        foreach ($this->jenisNilai as $jenis) {
            $this->assertTrue($jenis->validateNilai(50)); // Valid score
            $this->assertFalse($jenis->validateNilai(150)); // Invalid score
            $this->assertEquals(50, $jenis->calculateWeightedScore(50)); // With bobot 1.0
        }
    }
}
